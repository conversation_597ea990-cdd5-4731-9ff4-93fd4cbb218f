'use client'
import { useEffect, useRef } from 'react'
import { useTourStore } from '../stores/useTourStore'
import scenes from '../data/scenes.json'

export default function PanoramaViewer() {
  const containerRef = useRef<HTMLDivElement>(null)
  const viewerRef = useRef<any>(null)
  const current = useTourStore((s) => s.currentScene)
  const setScene = useTourStore((s) => s.setScene)

  useEffect(() => {
    if (!containerRef.current) return

    Promise.all([
      import('photo-sphere-viewer'),
      import('photo-sphere-viewer/dist/plugins/markers')
    ]).then(([{ Viewer }, { MarkersPlugin }]) => {
      viewerRef.current = new Viewer({
        container: containerRef.current!,
        panorama: scenes.find(s => s.id === current)!.image,
        defaultLat: 0,
        defaultLong: 0,
        mousewheel: false,
        plugins: [
          MarkersPlugin
        ],
        navbar: [],
        autorotateDelay: 1000,
        autorotateLat: 0,
        autorotateSpeed: '0.02rpm'
      })

      const plugin = viewerRef.current.getPlugin(MarkersPlugin)
      scenes.find(s => s.id === current)!.hotspots.forEach(h => {
        plugin.addMarker({
          id: h.id,
          longitude: h.yaw,
          latitude: h.pitch,
          html: `<div style="background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 3px; cursor: pointer;">${h.tooltip}</div>`,
          anchor: 'center center',
          tooltip: h.tooltip
        })
      })

      // Handle marker clicks
      viewerRef.current.on('select-marker', (e: any) => {
        const hotspot = scenes.find(s => s.id === current)!.hotspots.find(h => h.id === e.markerId)
        if (hotspot && hotspot.type === 'nav' && hotspot.target) {
          setScene(hotspot.target)
        }
      })
    })

    return () => viewerRef.current?.destroy()
  }, [current, setScene])

  return <div ref={containerRef} style={{ width: '100%', height: '100%' }} />
}
