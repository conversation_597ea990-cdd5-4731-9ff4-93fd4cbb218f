'use client'
import { useState } from 'react'
import { useTourStore } from '../stores/useTourStore'
import scenes from '../data/scenes.json'
import { IconButton, Box } from '@mui/material'
import PlayArrowIcon from '@mui/icons-material/PlayArrow'
import PauseIcon from '@mui/icons-material/Pause'

export default function VideoOverlay() {
  const current = useTourStore(s => s.currentScene)
  const [playing, setPlaying] = useState(false)
  const videoSrc = scenes.find(s => s.id === current)?.video

  if (!videoSrc) return null

  return (
    <Box sx={{ position: 'absolute', top: 16, right: 16, width: 320 }}>
      <Box component="video" src={videoSrc} width="100%" autoPlay={playing} controls />
      <IconButton onClick={() => setPlaying(p => !p)}>
        {playing ? <PauseIcon /> : <PlayArrowIcon />}
      </IconButton>
    </Box>
  )
}
