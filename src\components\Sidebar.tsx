'use client'
import { useTourStore } from '../stores/useTourStore'
import scenes from '../data/scenes.json'
import { Drawer, List, ListItemButton, ListItemText, Typography } from '@mui/material'

export default function Sidebar() {
  const current = useTourStore(s => s.currentScene)
  const setScene = useTourStore(s => s.setScene)

  return (
    <Drawer variant="permanent" anchor="left">
      <List>
        <Typography variant="h6" sx={{ m: 2 }}>Virtual Tour</Typography>
        {scenes.map(s => (
          <ListItemButton key={s.id} selected={s.id === current} onClick={() => setScene(s.id)}>
            <ListItemText primary={s.title} />
          </ListItemButton>
        ))}
      </List>
    </Drawer>
  )
}
